name: water_reminder_app
description: A comprehensive water reminder app with authentication, notifications, and Neon Postgres integration.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3

  # Navigation
  go_router: ^12.1.3

  # HTTP Client & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2

  # Notifications
  flutter_local_notifications: ^16.3.2
  timezone: ^0.9.2

  # Authentication (Firebase)
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3

  # UI & Design
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.9
  google_fonts: ^6.1.0
  flutter_animate: ^4.3.0

  # Utilities
  intl: ^0.19.0
  uuid: ^4.2.1
  equatable: ^2.0.5

  # Permissions
  permission_handler: ^11.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  retrofit_generator: ^8.0.6
  riverpod_generator: ^2.3.9
  json_serializable: ^6.7.1

  # Testing
  mockito: ^5.4.4
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/

  # fonts:
    # - family: CustomIcons
    #   fonts:
    #     - asset: assets/fonts/CustomIcons.ttf
