# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: runner
# Configuration: Debug
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__water_reminder_app
  depfile = $DEP_FILE
  deps = gcc
  command = /snap/flutter/current/usr/bin/clang++  $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__water_reminder_app
  command = $PRE_LINK && /snap/flutter/current/usr/bin/clang++  $FLAGS  $LINK_FLAGS $in  -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /snap/flutter/149/usr/bin/cmake -S/home/<USER>/Documents/flutter-app/linux -B/home/<USER>/Documents/flutter-app/build/linux/x64/debug
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /snap/flutter/current/usr/bin/ninja -t clean
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /snap/flutter/current/usr/bin/ninja -t targets
  description = All primary targets available:

