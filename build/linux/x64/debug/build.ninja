# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: runner
# Configuration: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include rules.ninja


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/Documents/flutter-app/linux -B/home/<USER>/Documents/flutter-app/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Documents/flutter-app/linux/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for install/strip

build flutter/CMakeFiles/install/strip.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build flutter/install/strip: phony flutter/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build flutter/CMakeFiles/install/local.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build flutter/install/local: phony flutter/CMakeFiles/install/local.util


#############################################
# Utility command for install

build flutter/CMakeFiles/install.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build flutter/install: phony flutter/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build flutter/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build flutter/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/Documents/flutter-app/linux -B/home/<USER>/Documents/flutter-app/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build flutter/rebuild_cache: phony flutter/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build flutter/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build flutter/edit_cache: phony flutter/CMakeFiles/edit_cache.util


#############################################
# Utility command for flutter_assemble

build flutter/flutter_assemble: phony flutter/CMakeFiles/flutter_assemble /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_


#############################################
# Phony custom command for flutter/CMakeFiles/flutter_assemble

build flutter/CMakeFiles/flutter_assemble: phony /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/flutter_linux.h


#############################################
# Custom command for /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/libflutter_linux_gtk.so

build /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/flutter && /snap/flutter/149/usr/bin/cmake -E env FLUTTER_ROOT=/home/<USER>/snap/flutter/common/flutter PROJECT_DIR=/home/<USER>/Documents/flutter-app DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMg==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049OGRlZmFhNzFhNw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTA5MTUwODkzOQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=/home/<USER>/Documents/flutter-app/.dart_tool/package_config.json FLUTTER_TARGET=/home/<USER>/Documents/flutter-app/lib/main.dart /home/<USER>/snap/flutter/common/flutter/packages/flutter_tools/bin/tool_backend.sh linux-x64 Debug
  DESC = Generating /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/libflutter_linux_gtk.so, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_engine.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_call.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_method_response.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_value.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/fl_view.h, /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/flutter_linux/flutter_linux.h, _phony_
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Documents/flutter-app/linux/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for install/strip

build runner/CMakeFiles/install/strip.util: CUSTOM_COMMAND runner/all
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build runner/install/strip: phony runner/CMakeFiles/install/strip.util


#############################################
# Utility command for install/local

build runner/CMakeFiles/install/local.util: CUSTOM_COMMAND runner/all
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build runner/install/local: phony runner/CMakeFiles/install/local.util


#############################################
# Utility command for install

build runner/CMakeFiles/install.util: CUSTOM_COMMAND runner/all
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build runner/install: phony runner/CMakeFiles/install.util


#############################################
# Utility command for list_install_components

build runner/list_install_components: phony


#############################################
# Utility command for rebuild_cache

build runner/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -S/home/<USER>/Documents/flutter-app/linux -B/home/<USER>/Documents/flutter-app/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build runner/rebuild_cache: phony runner/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build runner/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Documents/flutter-app/build/linux/x64/debug/runner && /snap/flutter/149/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build runner/edit_cache: phony runner/CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for EXECUTABLE target water_reminder_app


#############################################
# Order-only phony target for water_reminder_app

build cmake_object_order_depends_target_water_reminder_app: phony || flutter/flutter_assemble

build runner/CMakeFiles/water_reminder_app.dir/main.cc.o: CXX_COMPILER__water_reminder_app /home/<USER>/Documents/flutter-app/linux/runner/main.cc || cmake_object_order_depends_target_water_reminder_app
  DEFINES = -DAPPLICATION_ID=\"com.example.water_reminder_app\"
  DEP_FILE = runner/CMakeFiles/water_reminder_app.dir/main.cc.o.d
  FLAGS = -g   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Documents/flutter-app/linux -I/home/<USER>/Documents/flutter-app/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = runner/CMakeFiles/water_reminder_app.dir
  OBJECT_FILE_DIR = runner/CMakeFiles/water_reminder_app.dir
  TARGET_COMPILE_PDB = runner/CMakeFiles/water_reminder_app.dir/
  TARGET_PDB = intermediates_do_not_run/water_reminder_app.pdb

build runner/CMakeFiles/water_reminder_app.dir/my_application.cc.o: CXX_COMPILER__water_reminder_app /home/<USER>/Documents/flutter-app/linux/runner/my_application.cc || cmake_object_order_depends_target_water_reminder_app
  DEFINES = -DAPPLICATION_ID=\"com.example.water_reminder_app\"
  DEP_FILE = runner/CMakeFiles/water_reminder_app.dir/my_application.cc.o.d
  FLAGS = -g   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Documents/flutter-app/linux -I/home/<USER>/Documents/flutter-app/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = runner/CMakeFiles/water_reminder_app.dir
  OBJECT_FILE_DIR = runner/CMakeFiles/water_reminder_app.dir
  TARGET_COMPILE_PDB = runner/CMakeFiles/water_reminder_app.dir/
  TARGET_PDB = intermediates_do_not_run/water_reminder_app.pdb

build runner/CMakeFiles/water_reminder_app.dir/__/flutter/generated_plugin_registrant.cc.o: CXX_COMPILER__water_reminder_app /home/<USER>/Documents/flutter-app/linux/flutter/generated_plugin_registrant.cc || cmake_object_order_depends_target_water_reminder_app
  DEFINES = -DAPPLICATION_ID=\"com.example.water_reminder_app\"
  DEP_FILE = runner/CMakeFiles/water_reminder_app.dir/__/flutter/generated_plugin_registrant.cc.o.d
  FLAGS = -g   -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Documents/flutter-app/linux -I/home/<USER>/Documents/flutter-app/linux/flutter/ephemeral -isystem /snap/flutter/current/usr/include/gtk-3.0 -isystem /snap/flutter/current/usr/include/at-spi2-atk/2.0 -isystem /snap/flutter/current/usr/include/at-spi-2.0 -isystem /snap/flutter/current/usr/include/dbus-1.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/dbus-1.0/include -isystem /snap/flutter/current/usr/include/gio-unix-2.0 -isystem /snap/flutter/current/usr/include/cairo -isystem /snap/flutter/current/usr/include/pango-1.0 -isystem /snap/flutter/current/usr/include/fribidi -isystem /snap/flutter/current/usr/include/harfbuzz -isystem /snap/flutter/current/usr/include/atk-1.0 -isystem /snap/flutter/current/usr/include/pixman-1 -isystem /snap/flutter/current/usr/include/uuid -isystem /snap/flutter/current/usr/include/freetype2 -isystem /snap/flutter/current/usr/include/libpng16 -isystem /snap/flutter/current/usr/include/gdk-pixbuf-2.0 -isystem /snap/flutter/current/usr/include/libmount -isystem /snap/flutter/current/usr/include/blkid -isystem /snap/flutter/current/usr/include/glib-2.0 -isystem /snap/flutter/current/usr/lib/x86_64-linux-gnu/glib-2.0/include
  OBJECT_DIR = runner/CMakeFiles/water_reminder_app.dir
  OBJECT_FILE_DIR = runner/CMakeFiles/water_reminder_app.dir/__/flutter
  TARGET_COMPILE_PDB = runner/CMakeFiles/water_reminder_app.dir/
  TARGET_PDB = intermediates_do_not_run/water_reminder_app.pdb


# =============================================================================
# Link build statements for EXECUTABLE target water_reminder_app


#############################################
# Link the executable intermediates_do_not_run/water_reminder_app

build intermediates_do_not_run/water_reminder_app: CXX_EXECUTABLE_LINKER__water_reminder_app runner/CMakeFiles/water_reminder_app.dir/main.cc.o runner/CMakeFiles/water_reminder_app.dir/my_application.cc.o runner/CMakeFiles/water_reminder_app.dir/__/flutter/generated_plugin_registrant.cc.o | /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/libflutter_linux_gtk.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so || flutter/flutter_assemble
  FLAGS = -g
  LINK_FLAGS = -B/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -B/snap/flutter/current/usr/lib/x86_64-linux-gnu -B/snap/flutter/current/lib/x86_64-linux-gnu -B/snap/flutter/current/usr/lib/ -L/snap/flutter/current/usr/lib/gcc/x86_64-linux-gnu/9 -L/snap/flutter/current/usr/lib/x86_64-linux-gnu -L/snap/flutter/current/lib/x86_64-linux-gnu -L/snap/flutter/current/usr/lib/ -lblkid -lgcrypt -llzma -llz4 -lgpg-error -luuid -lpthread -ldl -lepoxy -lfontconfig
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/Documents/flutter-app/linux/flutter/ephemeral:  -lflutter_linux_gtk  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgtk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk-3.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libpango-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libharfbuzz.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libatk-1.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo-gobject.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libcairo.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgio-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libgobject-2.0.so  /snap/flutter/current/usr/lib/x86_64-linux-gnu/libglib-2.0.so
  LINK_PATH = -L/home/<USER>/Documents/flutter-app/linux/flutter/ephemeral
  OBJECT_DIR = runner/CMakeFiles/water_reminder_app.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = runner/CMakeFiles/water_reminder_app.dir/
  TARGET_FILE = intermediates_do_not_run/water_reminder_app
  TARGET_PDB = intermediates_do_not_run/water_reminder_app.pdb

# =============================================================================
# Target aliases.

build flutter_assemble: phony flutter/flutter_assemble

build water_reminder_app: phony intermediates_do_not_run/water_reminder_app

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/Documents/flutter-app/build/linux/x64/debug

build all: phony flutter/all runner/all

# =============================================================================

#############################################
# Folder: /home/<USER>/Documents/flutter-app/build/linux/x64/debug/flutter

build flutter/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/Documents/flutter-app/build/linux/x64/debug/runner

build runner/all: phony intermediates_do_not_run/water_reminder_app

# =============================================================================
# Built-in targets


#############################################
# Make the all target the default.

default all

#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/Documents/flutter-app/linux/CMakeLists.txt /home/<USER>/Documents/flutter-app/linux/flutter/CMakeLists.txt /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/Documents/flutter-app/linux/flutter/generated_plugins.cmake /home/<USER>/Documents/flutter-app/linux/runner/CMakeLists.txt /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXCompiler.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCompilerIdDetection.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCXXCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCompileFeatures.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCompilerABI.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCompilerId.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeFindBinUtils.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeNinjaFindMake.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeParseImplicitIncludeInfo.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeParseImplicitLinkInfo.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystem.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeTestCXXCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeTestCompilerCommon.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/ADSP-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/ARMCC-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/ARMClang-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/AppleClang-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Borland-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-FindBinUtils.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Cray-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GHS-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/IAR-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Intel-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/MSVC-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/PGI-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/PathScale-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/SCO-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/TI-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Watcom-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageMessage.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPkgConfig.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Internal/FeatureTesting.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Determine-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.16.3/CMakeCXXCompiler.cmake CMakeFiles/3.16.3/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/Documents/flutter-app/linux/CMakeLists.txt /home/<USER>/Documents/flutter-app/linux/flutter/CMakeLists.txt /home/<USER>/Documents/flutter-app/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/Documents/flutter-app/linux/flutter/generated_plugins.cmake /home/<USER>/Documents/flutter-app/linux/runner/CMakeLists.txt /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXCompiler.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeCompilerIdDetection.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCXXCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCompileFeatures.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCompilerABI.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineCompilerId.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeFindBinUtils.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeNinjaFindMake.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeParseImplicitIncludeInfo.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeParseImplicitLinkInfo.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystem.cmake.in /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeTestCXXCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/CMakeTestCompilerCommon.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/ADSP-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/ARMCC-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/ARMClang-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/AppleClang-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Borland-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang-FindBinUtils.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Clang.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Cray-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GHS-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/IAR-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Intel-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/MSVC-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/PGI-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/PathScale-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/SCO-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/TI-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/Watcom-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPackageMessage.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/FindPkgConfig.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Internal/FeatureTesting.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Clang-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-Determine-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/Linux.cmake /snap/flutter/149/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.16.3/CMakeCXXCompiler.cmake CMakeFiles/3.16.3/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP

