#!/bin/bash

# Water Reminder App Setup Script
echo "🚀 Setting up Water Reminder App..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter first."
    echo "Visit: https://docs.flutter.dev/get-started/install"
    exit 1
fi

echo "✅ Flutter found: $(flutter --version | head -n 1)"

# Check Flutter doctor
echo "🔍 Running Flutter doctor..."
flutter doctor

# Get dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

# Generate code
echo "🔧 Generating code..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Create necessary directories
echo "📁 Creating asset directories..."
mkdir -p assets/images
mkdir -p assets/icons
mkdir -p assets/fonts

# Create placeholder files
echo "📄 Creating placeholder files..."
touch assets/images/.gitkeep
touch assets/icons/.gitkeep
touch assets/fonts/.gitkeep

# Check for Firebase configuration
echo "🔥 Checking Firebase configuration..."
if [ ! -f "android/app/google-services.json" ]; then
    echo "⚠️  Warning: android/app/google-services.json not found"
    echo "   Please download it from Firebase Console and place it in android/app/"
fi

if [ ! -f "ios/Runner/GoogleService-Info.plist" ]; then
    echo "⚠️  Warning: ios/Runner/GoogleService-Info.plist not found"
    echo "   Please download it from Firebase Console and place it in ios/Runner/"
fi

# Run tests
echo "🧪 Running tests..."
flutter test

# Check for any issues
echo "🔍 Analyzing code..."
flutter analyze

echo ""
echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Add Firebase configuration files (google-services.json and GoogleService-Info.plist)"
echo "2. Update API base URL in lib/core/constants/app_constants.dart"
echo "3. Set up your Neon Postgres database"
echo "4. Run the app: flutter run"
echo ""
echo "📚 For more information, see README.md"
echo ""
echo "Happy coding! 💧"
