# 🚀 Getting Started with Water Reminder App

This guide will help you set up and run the Water Reminder Flutter app on your local machine.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

1. **Flutter SDK** (3.0.0 or higher)
   ```bash
   flutter --version
   ```

2. **Dart SDK** (3.0.0 or higher) - Usually comes with Flutter

3. **Android Studio** or **VS Code** with Flutter extensions

4. **Git** for version control

## 🛠️ Quick Setup

### Option 1: Automated Setup (Recommended)

Run the setup script to automatically configure the project:

```bash
./scripts/setup.sh
```

This script will:
- Check Flutter installation
- Install dependencies
- Generate necessary code
- Create asset directories
- Run tests and analysis

### Option 2: Manual Setup

1. **Install dependencies**
   ```bash
   flutter pub get
   ```

2. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

3. **Create asset directories**
   ```bash
   mkdir -p assets/{images,icons,fonts}
   ```

## 🔥 Firebase Configuration

### 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Follow the setup wizard
4. Enable Authentication with Email/Password

### 2. Add Android Configuration

1. In Firebase Console, click "Add app" → Android
2. Enter package name: `com.example.water_reminder_app`
3. Download `google-services.json`
4. Place it in `android/app/google-services.json`

### 3. Add iOS Configuration

1. In Firebase Console, click "Add app" → iOS
2. Enter bundle ID: `com.example.waterReminderApp`
3. Download `GoogleService-Info.plist`
4. Place it in `ios/Runner/GoogleService-Info.plist`

## 🗄️ Database Setup (Optional)

### Neon Postgres Setup

1. Create account at [Neon](https://neon.tech/)
2. Create a new database
3. Note your connection details
4. Update API URL in `lib/core/constants/app_constants.dart`

### Database Schema

Run these SQL commands in your Neon console:

```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    daily_goal INTEGER DEFAULT 2000,
    preferred_cup_size INTEGER DEFAULT 250,
    reminder_enabled BOOLEAN DEFAULT true,
    reminder_interval INTEGER DEFAULT 2,
    reminder_start_time TIME DEFAULT '08:00',
    reminder_end_time TIME DEFAULT '22:00',
    weight DECIMAL(5,2),
    activity_level VARCHAR(20),
    profile_image_url TEXT,
    timezone VARCHAR(50) DEFAULT 'UTC',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Water logs table
CREATE TABLE water_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    date DATE NOT NULL,
    note TEXT,
    source VARCHAR(50) DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_water_logs_user_date ON water_logs(user_id, date);
CREATE INDEX idx_water_logs_timestamp ON water_logs(timestamp);
```

## 🏃‍♂️ Running the App

### 1. Check Flutter Setup
```bash
flutter doctor
```
Resolve any issues shown.

### 2. List Available Devices
```bash
flutter devices
```

### 3. Run the App
```bash
# Run on connected device
flutter run

# Run on specific device
flutter run -d <device-id>

# Run in debug mode with hot reload
flutter run --debug

# Run in release mode
flutter run --release
```

### 4. Hot Reload
While the app is running, press:
- `r` for hot reload
- `R` for hot restart
- `q` to quit

## 🧪 Testing

### Run All Tests
```bash
flutter test
```

### Run Specific Test Files
```bash
# Unit tests
flutter test test/unit/

# Widget tests
flutter test test/widget/

# Integration tests
flutter test test/integration/
```

### Test Coverage
```bash
flutter test --coverage
```

## 🔧 Development Tools

### Code Analysis
```bash
flutter analyze
```

### Format Code
```bash
dart format .
```

### Generate Code
```bash
flutter packages pub run build_runner build
```

### Clean Build
```bash
flutter clean
flutter pub get
```

## 📱 App Features to Test

### 1. Authentication Flow
- Sign up with email/password
- Sign in with existing account
- Password reset functionality
- Form validation

### 2. Water Tracking
- Add water using quick buttons (150ml, 250ml, 500ml, etc.)
- Add custom water amount
- View daily progress
- Check water history

### 3. Notifications
- Enable/disable reminders
- Set reminder intervals
- Test notification permissions

### 4. Navigation
- Bottom navigation between Home, Stats, Profile
- Modal pages (Add Water, Settings, History)
- Back navigation

### 5. Data Persistence
- App state persists after restart
- User preferences saved locally
- Water logs stored locally

## 🐛 Troubleshooting

### Common Issues

1. **Build Errors**
   ```bash
   flutter clean
   flutter pub get
   flutter packages pub run build_runner build --delete-conflicting-outputs
   ```

2. **Firebase Issues**
   - Ensure configuration files are in correct locations
   - Check package names match Firebase project
   - Verify Authentication is enabled

3. **Dependency Issues**
   ```bash
   flutter pub deps
   flutter pub upgrade
   ```

4. **Android Build Issues**
   - Check Android SDK is installed
   - Verify ANDROID_HOME environment variable
   - Update Android build tools

5. **iOS Build Issues**
   - Open `ios/Runner.xcworkspace` in Xcode
   - Check signing certificates
   - Update iOS deployment target if needed

### Getting Help

1. Check the [Issues](../../issues) page
2. Run `flutter doctor -v` for detailed diagnostics
3. Check Flutter documentation
4. Create a new issue with:
   - Flutter version (`flutter --version`)
   - Error messages
   - Steps to reproduce

## 🎯 Next Steps

Once the app is running:

1. **Explore the UI** - Navigate through all screens
2. **Test Authentication** - Create an account and sign in
3. **Track Water** - Add some water intake logs
4. **Check Notifications** - Enable reminders and test
5. **Customize Settings** - Adjust daily goals and preferences
6. **Review Code** - Explore the clean architecture structure

## 📚 Learning Resources

- [Flutter Documentation](https://docs.flutter.dev/)
- [Riverpod Documentation](https://riverpod.dev/)
- [Firebase Flutter Setup](https://firebase.flutter.dev/)
- [Material 3 Design](https://m3.material.io/)
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)

---

**Happy Coding! 💧**

If you encounter any issues, please check the troubleshooting section or create an issue in the repository.
