# Generated code do not commit.
file(TO_CMAKE_PATH "/home/<USER>/snap/flutter/common/flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "/home/<USER>/Documents/flutter-app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=/home/<USER>/snap/flutter/common/flutter"
  "PROJECT_DIR=/home/<USER>/Documents/flutter-app"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMg==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049OGRlZmFhNzFhNw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTA5MTUwODkzOQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=/home/<USER>/Documents/flutter-app/.dart_tool/package_config.json"
  "FLUTTER_TARGET=/home/<USER>/Documents/flutter-app/lib/main.dart"
)
