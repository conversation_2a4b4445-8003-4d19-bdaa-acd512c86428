import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Statistics page showing water intake analytics
class StatisticsPage extends ConsumerWidget {
  const StatisticsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Statistics'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_month),
            onPressed: () {
              // TODO: Show date range picker
            },
            tooltip: 'Select Date Range',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Overview Cards
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    title: 'Today',
                    value: '1.2L',
                    subtitle: 'of 2.0L goal',
                    icon: Icons.today,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    context,
                    title: 'This Week',
                    value: '8.5L',
                    subtitle: 'Average: 1.2L/day',
                    icon: Icons.calendar_view_week,
                    color: Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    title: 'Streak',
                    value: '5 days',
                    subtitle: 'Goal achieved',
                    icon: Icons.local_fire_department,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    context,
                    title: 'Total',
                    value: '45.2L',
                    subtitle: 'This month',
                    icon: Icons.water_drop,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Weekly Chart Placeholder
            Text(
              'Weekly Progress',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Container(
                height: 200,
                padding: const EdgeInsets.all(16),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.bar_chart,
                        size: 48,
                        color: theme.colorScheme.primary.withOpacity(0.5),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Chart Coming Soon',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                      Text(
                        'Weekly water intake visualization',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Achievements Section
            Text(
              'Achievements',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildAchievementsList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementsList(BuildContext context) {
    final achievements = [
      _Achievement(
        icon: Icons.emoji_events,
        title: 'First Drop',
        description: 'Logged your first water intake',
        isUnlocked: true,
        color: Colors.green,
      ),
      _Achievement(
        icon: Icons.local_fire_department,
        title: '7-Day Streak',
        description: 'Achieved your goal for 7 consecutive days',
        isUnlocked: true,
        color: Colors.orange,
      ),
      _Achievement(
        icon: Icons.water_drop,
        title: 'Hydration Hero',
        description: 'Drink 100L of water total',
        isUnlocked: false,
        color: Colors.blue,
      ),
    ];

    return Column(
      children: achievements
          .map((achievement) => _buildAchievementItem(context, achievement))
          .toList(),
    );
  }

  Widget _buildAchievementItem(BuildContext context, _Achievement achievement) {
    final theme = Theme.of(context);

    return Card(
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: achievement.isUnlocked
                ? achievement.color.withOpacity(0.1)
                : theme.colorScheme.onSurface.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            achievement.icon,
            color: achievement.isUnlocked
                ? achievement.color
                : theme.colorScheme.onSurface.withOpacity(0.4),
          ),
        ),
        title: Text(
          achievement.title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: achievement.isUnlocked
                ? null
                : theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        subtitle: Text(
          achievement.description,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: achievement.isUnlocked
                ? theme.colorScheme.onSurface.withOpacity(0.7)
                : theme.colorScheme.onSurface.withOpacity(0.5),
          ),
        ),
        trailing: achievement.isUnlocked
            ? Icon(
                Icons.check_circle,
                color: achievement.color,
              )
            : Icon(
                Icons.lock_outline,
                color: theme.colorScheme.onSurface.withOpacity(0.4),
              ),
      ),
    );
  }
}

class _Achievement {
  final IconData icon;
  final String title;
  final String description;
  final bool isUnlocked;
  final Color color;

  _Achievement({
    required this.icon,
    required this.title,
    required this.description,
    required this.isUnlocked,
    required this.color,
  });
}
