# 💧 Water Reminder App

A comprehensive Flutter water reminder application with authentication, notifications, and Neon Postgres integration. Built with modern Flutter architecture patterns including BLoC/Clean Architecture, Riverpod state management, and Material 3 design.

## ✨ Features

### 🔐 Authentication
- **Firebase Authentication** with email/password
- Secure sign-up and sign-in flows
- Password reset functionality
- Form validation with proper error handling

### 💧 Water Tracking
- **Daily water intake tracking** with customizable goals
- **Quick add buttons** for common water amounts (150ml, 250ml, 500ml, etc.)
- **Custom amount entry** with visual feedback
- **Progress visualization** with circular progress indicators
- **Water intake history** with daily, weekly, and monthly views

### 🔔 Smart Notifications
- **Customizable reminders** with flexible intervals
- **Quiet hours** to avoid disturbing sleep
- **Achievement notifications** for goal completion
- **Local notifications** using flutter_local_notifications

### 📊 Analytics & Statistics
- **Daily progress tracking** with motivational messages
- **Weekly and monthly summaries**
- **Streak tracking** for consecutive goal achievements
- **Intake distribution** by time of day

### 🎨 Modern UI/UX
- **Material 3 design** with dynamic theming
- **Smooth animations** using flutter_animate
- **Responsive design** for different screen sizes
- **Dark/Light theme** support

### 🗄️ Data Management
- **Local storage** with Hive for offline functionality
- **Neon Postgres integration** for cloud sync
- **Clean Architecture** with proper separation of concerns
- **State management** with Riverpod

## 🏗️ Architecture

This app follows **Clean Architecture** principles with the following structure:

```
lib/
├── core/                   # Core utilities and constants
│   ├── constants/         # App-wide constants
│   ├── errors/           # Error handling and failures
│   ├── network/          # HTTP client configuration
│   ├── theme/            # App theming
│   └── utils/            # Utility functions
├── data/                  # Data layer
│   ├── datasources/      # Local and remote data sources
│   ├── models/           # Data models with JSON serialization
│   └── repositories/     # Repository implementations
├── domain/                # Domain layer
│   ├── entities/         # Business entities
│   ├── repositories/     # Repository interfaces
│   └── usecases/         # Business logic use cases
├── presentation/          # Presentation layer
│   ├── bloc/             # BLoC state management
│   ├── pages/            # UI screens
│   ├── providers/        # Riverpod providers
│   └── widgets/          # Reusable UI components
└── main.dart             # App entry point
```

## 🚀 Getting Started

### Prerequisites

- **Flutter SDK** (3.0.0 or higher)
- **Dart SDK** (3.0.0 or higher)
- **Firebase project** for authentication
- **Neon Postgres database** for cloud storage
- **Android Studio** or **VS Code** with Flutter extensions

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd water-reminder-app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Firebase Setup**
   - Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
   - Enable Authentication with Email/Password
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Place them in the appropriate directories:
     - Android: `android/app/google-services.json`
     - iOS: `ios/Runner/GoogleService-Info.plist`

5. **Neon Postgres Setup**
   - Create a Neon Postgres database at [Neon](https://neon.tech/)
   - Update the API base URL in `lib/core/constants/app_constants.dart`
   - Configure your database schema (see Database Schema section)

6. **Run the app**
   ```bash
   flutter run
   ```

## 🗃️ Database Schema

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    daily_goal INTEGER DEFAULT 2000,
    preferred_cup_size INTEGER DEFAULT 250,
    reminder_enabled BOOLEAN DEFAULT true,
    reminder_interval INTEGER DEFAULT 2,
    reminder_start_time TIME DEFAULT '08:00',
    reminder_end_time TIME DEFAULT '22:00',
    weight DECIMAL(5,2),
    activity_level VARCHAR(20),
    profile_image_url TEXT,
    timezone VARCHAR(50) DEFAULT 'UTC',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Water Logs Table
```sql
CREATE TABLE water_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    date DATE NOT NULL,
    note TEXT,
    source VARCHAR(50) DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_water_logs_user_date ON water_logs(user_id, date);
CREATE INDEX idx_water_logs_timestamp ON water_logs(timestamp);
```

## 🧪 Testing

### Run Unit Tests
```bash
flutter test test/unit/
```

### Run Widget Tests
```bash
flutter test test/widget/
```

### Run Integration Tests
```bash
flutter test test/integration/
```

### Run All Tests
```bash
flutter test
```

### Test Coverage
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## 📱 Key Dependencies

### Core Dependencies
- **flutter_riverpod**: State management
- **go_router**: Navigation and routing
- **hive & hive_flutter**: Local database
- **dio**: HTTP client for API calls
- **firebase_auth & firebase_core**: Authentication

### UI Dependencies
- **google_fonts**: Custom fonts
- **flutter_animate**: Smooth animations
- **flutter_svg**: SVG support

### Utility Dependencies
- **intl**: Internationalization and date formatting
- **shared_preferences**: Simple key-value storage
- **flutter_local_notifications**: Local notifications
- **permission_handler**: Runtime permissions

### Development Dependencies
- **build_runner**: Code generation
- **hive_generator**: Hive type adapters
- **json_serializable**: JSON serialization
- **flutter_lints**: Linting rules

## 🔧 Configuration

### App Constants
Update `lib/core/constants/app_constants.dart` with your configuration:

```dart
class AppConstants {
  // API Configuration
  static const String baseUrl = 'https://your-neon-postgres-api.com/api/v1';
  
  // Default Settings
  static const int defaultDailyGoal = 2000; // ml
  static const int defaultCupSize = 250; // ml
  static const int defaultReminderInterval = 2; // hours
}
```

### Notification Setup
The app uses local notifications for water reminders. Permissions are requested automatically on first launch.

### Theme Customization
Modify `lib/core/theme/app_theme.dart` to customize colors, fonts, and styling.

## 🚀 Deployment

### Android
1. **Build APK**
   ```bash
   flutter build apk --release
   ```

2. **Build App Bundle**
   ```bash
   flutter build appbundle --release
   ```

### iOS
1. **Build iOS**
   ```bash
   flutter build ios --release
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style
- Follow the [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use the provided linting rules in `analysis_options.yaml`
- Write tests for new features
- Document public APIs

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Flutter Team** for the amazing framework
- **Firebase** for authentication services
- **Neon** for serverless Postgres
- **Material Design** for UI guidelines
- **Community packages** that made this project possible

## 📞 Support

If you have any questions or need help, please:
1. Check the [Issues](../../issues) page
2. Create a new issue with detailed information
3. Contact the development team

---

**Happy Hydrating! 💧**
